package com.example.meals.dto;

/**
 * 创建管理员请求DTO
 */
public class AdminCreateRequest {
    
    private String username; // 用户名
    private String password; // 密码
    private String email;    // 邮箱
    private String realName; // 真实姓名
    
    // 构造函数
    public AdminCreateRequest() {}
    
    public AdminCreateRequest(String username, String password, String email, String realName) {
        this.username = username;
        this.password = password;
        this.email = email;
        this.realName = realName;
    }
    
    // Getter 和 Setter 方法
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public String getPassword() {
        return password;
    }
    
    public void setPassword(String password) {
        this.password = password;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public String getRealName() {
        return realName;
    }
    
    public void setRealName(String realName) {
        this.realName = realName;
    }
    

    
    @Override
    public String toString() {
        return "AdminCreateRequest{" +
                "username='" + username + '\'' +
                ", password='[PROTECTED]'" +
                ", email='" + email + '\'' +
                ", realName='" + realName + '\'' +
                '}';
    }
}
