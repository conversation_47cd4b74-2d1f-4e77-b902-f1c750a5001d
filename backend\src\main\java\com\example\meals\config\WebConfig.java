package com.example.meals.config;

import com.example.meals.interceptor.JwtInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web 配置类
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {
    
    @Autowired
    private JwtInterceptor jwtInterceptor;
    
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(jwtInterceptor)
                // 需要JWT认证的路径
                .addPathPatterns("/api/**")
                // 排除不需要认证的路径
                .excludePathPatterns(
                        "/api/user/login",           // 用户登录
                        "/api/user/register",        // 用户注册
                        "/api/user/check-username",  // 检查用户名
                        "/api/user/check-email",     // 检查邮箱
                        "/api/user/check-phone",     // 检查手机号
                        "/api/admin/login",          // 管理员登录
                        "/api/core-features/enabled" // 获取启用的核心功能（公开接口）
                );
    }
    
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                // 使用allowedOriginPatterns支持通配符，同时支持凭据
                .allowedOriginPatterns(
                    "http://localhost:*",
                    "http://127.0.0.1:*"
                )
                // 允许的HTTP方法
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                // 允许的请求头
                .allowedHeaders("*")
                // 允许凭据
                .allowCredentials(true)
                // 暴露的响应头
                .exposedHeaders("Authorization", "Content-Type")
                // 预检请求缓存时间
                .maxAge(3600);
    }
}
