package com.example.meals.service;

import com.example.meals.dto.*;
import com.example.meals.common.Result;

/**
 * 管理员服务接口
 */
public interface AdminService {
    
    /**
     * 管理员登录
     */
    Result<AdminResponse> login(AdminLoginRequest request);
    
    /**
     * 创建管理员
     */
    Result<AdminResponse> createAdmin(AdminCreateRequest request);
    
    /**
     * 更新管理员信息
     */
    Result<AdminResponse> updateAdmin(Long id, AdminCreateRequest request);
    
    /**
     * 删除管理员
     */
    Result<Void> deleteAdmin(Long id);
    
    /**
     * 根据ID获取管理员信息
     */
    Result<AdminResponse> getAdminById(Long id);
    
    /**
     * 分页查询管理员列表
     */
    Result<PageResponse<AdminResponse>> getAdminList(Integer page, Integer size,
                                                     String username, String realName,
                                                     Integer status);
    
    /**
     * 启用/禁用管理员
     */
    Result<Void> toggleAdminStatus(Long id, Integer status);
    
    /**
     * 重置管理员密码
     */
    Result<Void> resetPassword(Long id, String newPassword);
    
    /**
     * 检查用户名是否可用
     */
    Result<Boolean> checkUsername(String username);
    
    /**
     * 检查邮箱是否可用
     */
    Result<Boolean> checkEmail(String email);

    // ==================== 用户管理功能 ====================

    /**
     * 分页查询用户列表（管理员功能）
     */
    Result<PageResponse<UserResponse>> getUserList(Integer page, Integer size,
                                                   String username, String email,
                                                   String phone, Integer status);

    /**
     * 启用/禁用用户
     */
    Result<Void> toggleUserStatus(Long id, Integer status);

    /**
     * 删除用户（管理员功能）
     */
    Result<Void> deleteUser(Long id);

    /**
     * 获取用户统计信息
     */
    Result<UserStatsResponse> getUserStats();
}
