package com.example.meals.controller;

import com.example.meals.dto.*;
import com.example.meals.service.AdminService;
import com.example.meals.common.Result;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.web.bind.annotation.*;

/**
 * 管理员控制器
 */
@RestController
@RequestMapping("/api/admin")
public class AdminController {

    @Autowired
    private AdminService adminService;
    
    /**
     * 管理员登录
     */
    @PostMapping("/login")
    public Result<AdminResponse> login(@RequestBody AdminLoginRequest request) {
        return adminService.login(request);
    }


    

    

    
    /**
     * 删除管理员
     */
    @DeleteMapping("/{id}")
    public Result<Void> deleteAdmin(@PathVariable Long id) {
        return adminService.deleteAdmin(id);
    }
    
    /**
     * 根据ID获取管理员信息
     */
    @GetMapping("/{id}")
    public Result<AdminResponse> getAdminById(@PathVariable Long id) {
        return adminService.getAdminById(id);
    }
    
    /**
     * 分页查询管理员列表
     */
    @GetMapping("/list")
    public Result<PageResponse<AdminResponse>> getAdminList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String username,
            @RequestParam(required = false) String realName,
            @RequestParam(required = false) Integer status) {
        return adminService.getAdminList(page, size, username, realName, status);
    }
    
    /**
     * 启用/禁用管理员
     */
    @PutMapping("/{id}/status")
    public Result<Void> toggleAdminStatus(@PathVariable Long id, @RequestParam Integer status) {
        return adminService.toggleAdminStatus(id, status);
    }
    
    /**
     * 重置管理员密码
     */
    @PutMapping("/{id}/reset-password")
    public Result<Void> resetPassword(@PathVariable Long id, @RequestParam String newPassword) {
        return adminService.resetPassword(id, newPassword);
    }
    
    /**
     * 检查用户名是否可用
     */
    @GetMapping("/check-username")
    public Result<Boolean> checkUsername(@RequestParam String username) {
        return adminService.checkUsername(username);
    }
    
    /**
     * 检查邮箱是否可用
     */
    @GetMapping("/check-email")
    public Result<Boolean> checkEmail(@RequestParam String email) {
        return adminService.checkEmail(email);
    }

    // ==================== 用户管理接口 ====================

    /**
     * 分页查询用户列表（管理员功能）
     */
    @GetMapping("/users")
    public Result<PageResponse<UserResponse>> getUserList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String username,
            @RequestParam(required = false) String email,
            @RequestParam(required = false) String phone,
            @RequestParam(required = false) Integer status) {
        return adminService.getUserList(page, size, username, email, phone, status);
    }

    /**
     * 启用/禁用用户
     */
    @PutMapping("/users/{id}/status")
    public Result<Void> toggleUserStatus(@PathVariable Long id, @RequestParam Integer status) {
        return adminService.toggleUserStatus(id, status);
    }

    /**
     * 删除用户（管理员功能）
     */
    @DeleteMapping("/users/{id}")
    public Result<Void> deleteUser(@PathVariable Long id) {
        return adminService.deleteUser(id);
    }

    /**
     * 获取用户统计信息
     */
    @GetMapping("/users/stats")
    public Result<UserStatsResponse> getUserStats() {
        return adminService.getUserStats();
    }
}
