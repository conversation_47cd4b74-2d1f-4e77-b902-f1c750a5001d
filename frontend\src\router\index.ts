import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'
import { authGuard, adminGuard, guestGuard, userTypeGuard } from './guards'
import LoginView from '../views/LoginView.vue'
import RegisterView from '../views/RegisterView.vue'
import DashboardView from '../views/DashboardView.vue'
import AdminLoginView from '../views/AdminLoginView.vue'
import AdminDashboardView from '../views/AdminDashboardView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
      meta: {
        title: '膳食营养分析平台 - 智能营养管理'
      }
    },
    {
      path: '/login',
      name: 'login',
      component: LoginView,
      beforeEnter: guestGuard,
      meta: {
        title: '登录 - 膳食营养分析平台'
      }
    },
    {
      path: '/register',
      name: 'register',
      component: RegisterView,
      beforeEnter: guestGuard,
      meta: {
        title: '注册 - 膳食营养分析平台'
      }
    },
    {
      path: '/dashboard',
      name: 'dashboard',
      component: DashboardView,
      beforeEnter: userTypeGuard(['USER']),
      meta: {
        title: '控制台 - 膳食营养分析平台'
      }
    },
    {
      path: '/admin/login',
      name: 'admin-login',
      component: AdminLoginView,
      beforeEnter: guestGuard,
      meta: {
        title: '管理员登录 - 膳食营养分析平台'
      }
    },
    {
      path: '/admin/dashboard',
      name: 'admin-dashboard',
      component: AdminDashboardView,
      beforeEnter: adminGuard,
      meta: {
        title: '管理后台 - 膳食营养分析平台'
      }
    }
  ]
})

// 路由守卫 - 设置页面标题
router.beforeEach((to) => {
  if (to.meta.title) {
    document.title = to.meta.title as string
  }
})

export default router
