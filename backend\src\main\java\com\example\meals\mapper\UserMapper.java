package com.example.meals.mapper;

import com.example.meals.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 用户数据访问层
 */
@Mapper
public interface UserMapper {
    
    /**
     * 根据ID查询用户
     */
    User selectById(@Param("id") Long id);
    
    /**
     * 根据用户名查询用户
     */
    User selectByUsername(@Param("username") String username);
    
    /**
     * 根据邮箱查询用户
     */
    User selectByEmail(@Param("email") String email);
    
    /**
     * 根据手机号查询用户
     */
    User selectByPhone(@Param("phone") String phone);
    
    /**
     * 根据邮箱或手机号查询用户（用于登录）
     */
    User selectByEmailOrPhone(@Param("emailOrPhone") String emailOrPhone);
    
    /**
     * 插入用户
     */
    int insert(User user);
    
    /**
     * 更新用户信息
     */
    int updateById(User user);
    
    /**
     * 根据ID删除用户
     */
    int deleteById(@Param("id") Long id);
    
    /**
     * 检查用户名是否存在
     */
    int countByUsername(@Param("username") String username);
    
    /**
     * 检查邮箱是否存在
     */
    int countByEmail(@Param("email") String email);
    
    /**
     * 检查手机号是否存在
     */
    int countByPhone(@Param("phone") String phone);

    /**
     * 分页查询用户列表（管理员功能）
     */
    List<User> selectByCondition(@Param("username") String username,
                                 @Param("email") String email,
                                 @Param("phone") String phone,
                                 @Param("status") Integer status,
                                 @Param("offset") Integer offset,
                                 @Param("limit") Integer limit);

    /**
     * 根据条件统计用户总数（管理员功能）
     */
    int countByCondition(@Param("username") String username,
                         @Param("email") String email,
                         @Param("phone") String phone,
                         @Param("status") Integer status);

    /**
     * 统计用户总数
     */
    int countAll();

    /**
     * 统计今日活跃用户数（简化实现：今日注册用户数）
     */
    int countTodayActive();
}
