<template>
  <div class="home-container">
    <!-- 顶部导航栏 -->
    <nav class="top-navbar">
      <div class="nav-content">
        <div class="nav-brand">
          <div class="brand-logo">
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" 
                    fill="currentColor"/>
              <circle cx="12" cy="12" r="3" fill="none" stroke="currentColor" stroke-width="2"/>
            </svg>
          </div>
          <span class="brand-name">膳食营养分析平台</span>
        </div>
        <div class="nav-actions">
          <template v-if="isLoggedIn">
            <span class="welcome-text">欢迎，{{ user?.username }}</span>
            <router-link to="/dashboard" class="nav-btn dashboard-btn">控制台</router-link>
            <button @click="handleLogout" class="nav-btn logout-btn">退出登录</button>
          </template>
          <template v-else>
            <router-link to="/login" class="nav-btn login-btn">登录</router-link>
            <router-link to="/register" class="nav-btn register-btn">注册</router-link>
          </template>
        </div>
      </div>
    </nav>

    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="circle circle-1"></div>
      <div class="circle circle-2"></div>
      <div class="circle circle-3"></div>
    </div>

    <!-- 英雄区域 -->
    <section class="hero-section">
      <div class="hero-content">
        <h1 class="hero-title">科学饮食，智慧健康</h1>
        <p class="hero-subtitle">
          基于AI技术的个性化营养分析平台，为您提供专业的膳食指导和健康管理服务
        </p>
        <div class="hero-features">
          <div class="feature-tag">🥗 智能营养分析</div>
          <div class="feature-tag">📊 个性化建议</div>
          <div class="feature-tag">🎯 健康目标管理</div>
        </div>
        <div class="hero-actions">
          <router-link to="/login" class="cta-btn primary">立即开始</router-link>
          <button class="cta-btn secondary" @click="scrollToFeatures">了解更多</button>
        </div>
      </div>
    </section>

    <!-- 核心功能模块展示 -->
    <section class="features-section" ref="featuresSection">
      <div class="section-content">
        <div class="section-header">
          <h2>九大核心功能</h2>
          <p>全方位的营养健康管理服务，从基础记录到智能分析</p>
        </div>
        
        <!-- 加载状态 -->
        <div v-if="isLoadingFeatures" class="loading-container">
          <div class="loading-spinner"></div>
          <p>正在加载核心功能...</p>
        </div>

        <!-- 功能网格 -->
        <div v-else class="features-grid">
          <div
            v-for="feature in features"
            :key="feature.id"
            class="feature-card"
            @click="handleFeatureClick(feature)"
          >
            <div class="feature-icon" v-html="feature.icon"></div>
            <h3 class="feature-title">{{ feature.title }}</h3>
            <p class="feature-description">{{ feature.description }}</p>
            <div class="feature-highlights">
              <span
                v-for="highlight in feature.highlights"
                :key="highlight"
                class="highlight-tag"
              >
                {{ highlight }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 底部信息 -->
    <footer class="footer-section">
      <div class="footer-content">
        <div class="footer-brand">
          <div class="brand-logo">
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" 
                    fill="currentColor"/>
              <circle cx="12" cy="12" r="3" fill="none" stroke="currentColor" stroke-width="2"/>
            </svg>
          </div>
          <span class="brand-name">膳食营养分析平台</span>
        </div>
        <div class="footer-info">
          <p>&copy; 2025 膳食营养分析平台. 智能营养管理服务</p>
          <p>科学饮食，健康生活</p>
          <div class="admin-link">
            <router-link to="/admin/login" class="admin-login-link">管理员登录</router-link>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuth } from '../composables/useAuth'
import { getAllEnabledCoreFeatures, type CoreFeature } from '../utils/coreFeatureApi'

const router = useRouter()
const { user, isLoggedIn, initUser, logout, updateUserState } = useAuth()
const featuresSection = ref<HTMLElement>()

// 核心功能数据
const features = ref<CoreFeature[]>([])
const isLoadingFeatures = ref(false)

// 加载核心功能数据
const loadCoreFeatures = async () => {
  isLoadingFeatures.value = true
  try {
    const response = await getAllEnabledCoreFeatures()
    if (response.success && response.data) {
      features.value = response.data
    } else {
      console.error('加载核心功能失败:', response.message)
    }
  } catch (error) {
    console.error('加载核心功能失败:', error)
  } finally {
    isLoadingFeatures.value = false
  }
}

// 滚动到功能区域
const scrollToFeatures = () => {
  featuresSection.value?.scrollIntoView({ behavior: 'smooth' })
}

// 处理功能卡片点击
const handleFeatureClick = (feature: any) => {
  if (!isLoggedIn.value) {
    router.push('/login')
    return
  }

  // 功能暂未开放，所有业务功能已被清理
  alert('该功能正在开发中，敬请期待！')
}

// 处理登出
const handleLogout = () => {
  logout()
  // 立即更新状态
  updateUserState()
  // 可以不跳转，让用户留在首页看到状态变化
  // router.push('/')
}

onMounted(() => {
  // 初始化用户状态
  initUser()
  // 加载核心功能数据
  loadCoreFeatures()
  // 页面加载动画等初始化逻辑
})
</script>

<style scoped>
/* CSS变量定义 */
:root {
  --primary-color: #16a085;
  --primary-light: #1abc9c;
  --primary-dark: #138d75;
  --secondary-color: #3498db;
  --accent-color: #e74c3c;
  --text-primary: #2c3e50;
  --text-secondary: #7f8c8d;
  --text-light: #bdc3c7;
  --background-light: #f8f9fa;
  --background-white: #ffffff;
  --border-color: #e9ecef;
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.15);
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
}

/* 全局样式 */
.home-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  position: relative;
  overflow-x: hidden;
}

/* 背景装饰 */
.background-decoration {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(22, 160, 133, 0.1) 0%, rgba(26, 188, 156, 0.05) 100%);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 300px;
  height: 300px;
  top: -150px;
  right: -150px;
  animation-delay: 0s;
}

.circle-2 {
  width: 200px;
  height: 200px;
  bottom: -100px;
  left: -100px;
  animation-delay: 2s;
}

.circle-3 {
  width: 150px;
  height: 150px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

/* 顶部导航栏 */
.top-navbar {
  position: sticky;
  top: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border-color);
  z-index: 100;
  padding: 1rem 0;
}

.nav-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.brand-logo {
  width: 40px;
  height: 40px;
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
}

.brand-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.nav-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

/* AURA-X: Modify - 顶部导航登录/注册按钮UI重构（Confirmed via 寸止） */
/* 统一导航按钮基础样式：尺寸、对齐、过渡与可访问性基线 */
.nav-btn {
  padding: 0.5rem 1.25rem; /* 稍微收紧，避免小屏拥挤 */
  border-radius: var(--radius-md);
  text-decoration: none;
  font-weight: 600;
  transition: background-color 0.2s ease, color 0.2s ease, transform 0.15s ease, box-shadow 0.2s ease, border-color 0.2s ease;
  border: 2px solid transparent;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  line-height: 1.2;
}

/* 焦点可见环（键盘导航） */
.nav-btn:focus { outline: none; }
.nav-btn:focus-visible {
  outline: none;
  box-shadow: 0 0 0 3px rgba(26, 188, 156, 0.35); /* 使用主色系高对比聚焦环 */
}

/* 禁用/不可交互状态（支持原生和 aria-disabled） */
.nav-btn:disabled,
.nav-btn[aria-disabled="true"] {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
  filter: saturate(0.85);
}

/* 登录按钮：Ghost/线框风格（层级较低） */
.nav-btn.login-btn,
.nav-btn.login-btn:link,
.nav-btn.login-btn:visited {
  color: var(--primary-color);
  border-color: var(--primary-color);
  background: transparent;
  text-decoration: none;
}

/* AURA-X: Modify - 修复登录按钮悬停样式，确保文字可见性 (Confirmed via 寸止) */
.nav-btn.login-btn:hover {
  background: #16a085 !important;  /* 直接使用绿色值确保背景色正确 */
  color: #ffffff !important;       /* 强制白色文字确保对比度 */
  border-color: #16a085 !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(22, 160, 133, 0.28);
}
.nav-btn.login-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(22, 160, 133, 0.22);
  background: #138d75 !important;  /* 稍深的绿色用于按下状态 */
  color: #ffffff !important;
}
.nav-btn.login-btn:focus-visible {
  border-color: var(--primary-dark);
}

/* 注册按钮：Solid/实底渐变（主行动） */
/* AURA-X: Modify - 修复注册按钮基础样式，确保按钮可见 (Confirmed via 寸止) */
.nav-btn.register-btn {
  background: linear-gradient(135deg, #16a085 0%, #138d75 100%) !important;  /* 使用直接颜色值确保可见 */
  color: #ffffff !important;  /* 强制白色文字 */
  border-color: transparent;
}

/* AURA-X: Modify - 修复注册按钮悬停样式，确保文字可见性 (Confirmed via 寸止) */
.nav-btn.register-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(22, 160, 133, 0.28);
  background: linear-gradient(135deg, #138d75 0%, #0f7d68 100%) !important;  /* 使用直接颜色值 */
  color: #ffffff !important;  /* 强制白色文字确保对比度 */
}
.nav-btn.register-btn:active {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(22, 160, 133, 0.22);
  background: linear-gradient(135deg, #0f7d68 0%, #0d6b5c 100%) !important;  /* 按下状态更深色调 */
  color: #ffffff !important;
}
.nav-btn.register-btn:focus-visible {
  box-shadow: 0 0 0 3px rgba(26, 188, 156, 0.35), 0 8px 24px rgba(22, 160, 133, 0.28);
}

.welcome-text {
  color: #4b5563;
  font-weight: 500;
  margin-right: 1rem;
}

/* 已登录状态按钮：保持原有样式，确保兼容性 */
.dashboard-btn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.logout-btn {
  background: #ef4444;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.logout-btn:hover {
  background: #dc2626;
}

/* 英雄区域 */
.hero-section {
  position: relative;
  z-index: 1;
  padding: 4rem 0 6rem 0;
  text-align: center;
}

.hero-content {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* AURA-X: Modify - 修复标题可见性问题，确保渐变文字效果正常显示 (Confirmed via 寸止) */
.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  color: #2c3e50;  /* 备用颜色，防止渐变失效时文字不可见 */
  margin-bottom: 1.5rem;
  line-height: 1.2;
  background: linear-gradient(135deg, #16a085 0%, #1abc9c 100%);  /* 使用直接颜色值确保渐变生效 */
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  /* 添加浏览器兼容性支持 */
  -moz-background-clip: text;
  -moz-text-fill-color: transparent;
}

/* 为不支持background-clip的浏览器提供备用样式 */
@supports not (-webkit-background-clip: text) {
  .hero-title {
    color: #16a085 !important;  /* 使用主题绿色作为备用 */
    background: none;
  }
}

.hero-subtitle {
  font-size: 1.25rem;
  color: var(--text-secondary);
  margin-bottom: 2rem;
  line-height: 1.6;
}

.hero-features {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 3rem;
  flex-wrap: wrap;
}

.feature-tag {
  background: rgba(22, 160, 133, 0.1);
  color: var(--primary-color);
  padding: 0.5rem 1rem;
  border-radius: var(--radius-lg);
  font-weight: 500;
  font-size: 0.9rem;
}

.hero-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.cta-btn {
  padding: 1rem 2rem;
  border-radius: var(--radius-lg);
  font-weight: 600;
  font-size: 1.1rem;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 150px;
}

.cta-btn.primary {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  color: white;
  box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4), 0 4px 12px rgba(0, 0, 0, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
  font-weight: 700;
}

.cta-btn.primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.cta-btn.primary:hover::before {
  left: 100%;
}

.cta-btn.primary:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 35px rgba(231, 76, 60, 0.6), 0 6px 20px rgba(0, 0, 0, 0.25);
  background: linear-gradient(135deg, #c0392b 0%, #a93226 100%);
}

.cta-btn.secondary {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.4);
  box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4), 0 3px 10px rgba(0, 0, 0, 0.15);
  font-weight: 600;
  position: relative;
  overflow: hidden;
}

.cta-btn.secondary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.cta-btn.secondary:hover::before {
  left: 100%;
}

.cta-btn.secondary:hover {
  background: linear-gradient(135deg, #2980b9 0%, #1f618d 100%);
  color: white;
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(52, 152, 219, 0.5), 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* 通用区域样式 */
.section-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-header h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.section-header p {
  font-size: 1.1rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* 功能模块展示 */
.features-section {
  position: relative;
  z-index: 1;
  padding: 6rem 0;
  background: white;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 0;
  color: var(--text-muted);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border-light);
  border-top: 3px solid var(--primary-green);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.feature-card {
  background: white;
  border-radius: var(--radius-xl);
  padding: 2rem;
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.feature-card:hover::before {
  transform: scaleX(1);
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-medium);
}

.feature-icon {
  width: 60px;
  height: 60px;
  color: var(--primary-color);
  margin-bottom: 1.5rem;
}

.feature-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.feature-description {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.feature-highlights {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.highlight-tag {
  background: var(--background-light);
  color: var(--text-secondary);
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-sm);
  font-size: 0.85rem;
  font-weight: 500;
}



/* 底部信息 */
.footer-section {
  position: relative;
  z-index: 1;
  padding: 4rem 0;
  background: linear-gradient(135deg, #1a252f 0%, #2c3e50 100%);
  color: white;
  border-top: 3px solid var(--primary-light);
}

.footer-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(22, 160, 133, 0.1) 0%, rgba(26, 188, 156, 0.05) 100%);
  pointer-events: none;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  text-align: center;
  position: relative;
  z-index: 1;
}

.footer-brand {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-xl);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.footer-brand .brand-logo {
  color: var(--primary-light);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
  width: 50px;
  height: 50px;
}

.footer-brand .brand-name {
  color: #ffffff;
  font-size: 1.4rem;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: 0.5px;
}

.footer-info {
  margin-top: 1.5rem;
}

.footer-info p {
  margin: 0.75rem 0;
  color: #e8f4f8;
  font-size: 1rem;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  line-height: 1.6;
}

.footer-info p:first-child {
  font-size: 1.1rem;
  font-weight: 600;
  color: #ffffff;
}

.footer-info p:last-child {
  font-style: italic;
  color: var(--primary-light);
  font-weight: 600;
}

.admin-link {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.admin-login-link {
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  font-size: 0.8rem;
  transition: color 0.3s ease;
}

.admin-login-link:hover {
  color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .nav-content {
    padding: 0 1rem;
  }

  /* 移动端导航按钮优化：减小尺寸，保证并排显示 */
  .nav-btn {
    padding: 0.375rem 1rem;
    font-size: 0.875rem;
    font-weight: 600;
  }

  .nav-brand .brand-name {
    display: none;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.1rem;
  }

  .hero-features {
    flex-direction: column;
    align-items: center;
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
  }

  .cta-btn {
    width: 100%;
    max-width: 300px;
  }

  .section-content {
    padding: 0 1rem;
  }

  .section-header h2 {
    font-size: 2rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .feature-card {
    padding: 1.5rem;
  }





  .footer-section {
    padding: 3rem 0;
  }

  .footer-brand {
    flex-direction: column;
    gap: 0.75rem;
    padding: 1rem;
  }

  .footer-brand .brand-logo {
    width: 40px;
    height: 40px;
  }

  .footer-brand .brand-name {
    font-size: 1.2rem;
  }

  .footer-info p {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .hero-section {
    padding: 2rem 0 4rem 0;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .feature-tag {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
  }

  .cta-btn {
    padding: 0.8rem 1.5rem;
    font-size: 1rem;
  }

  .features-section {
    padding: 4rem 0;
  }

  .section-header {
    margin-bottom: 3rem;
  }

  .section-header h2 {
    font-size: 1.75rem;
  }

  .footer-section {
    padding: 2.5rem 0;
  }

  .footer-brand {
    margin-bottom: 1.5rem;
    padding: 0.75rem;
  }

  .footer-brand .brand-name {
    font-size: 1.1rem;
  }

  .footer-info p {
    font-size: 0.85rem;
    margin: 0.5rem 0;
  }

  .footer-info p:first-child {
    font-size: 0.95rem;
  }
}
</style>
