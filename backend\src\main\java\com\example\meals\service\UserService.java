package com.example.meals.service;

import com.example.meals.dto.LoginRequest;
import com.example.meals.dto.RegisterRequest;
import com.example.meals.dto.UserResponse;
import com.example.meals.common.Result;

/**
 * 用户服务接口
 */
public interface UserService {
    
    /**
     * 用户注册
     */
    Result<UserResponse> register(RegisterRequest request);
    
    /**
     * 用户登录
     */
    Result<UserResponse> login(LoginRequest request);
    
    /**
     * 检查用户名是否可用
     */
    Result<Boolean> checkUsername(String username);
    
    /**
     * 检查邮箱是否可用
     */
    Result<Boolean> checkEmail(String email);
    
    /**
     * 检查手机号是否可用
     */
    Result<Boolean> checkPhone(String phone);
    
    /**
     * 根据ID获取用户信息
     */
    Result<UserResponse> getUserById(Long id);
}
